#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序3 - 网络监控模拟
独立运行的示例程序，可在新窗口中显示
"""

import time
import random
import datetime
import sys

def main():
    print("=== Network Monitor (Program 3) ===")
    print("Running in separate window")
    print("Press Ctrl+C to exit")
    print()

    try:
        counter = 0
        while True:
            counter += 1
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # 模拟网络数据
            download_speed = random.randint(50, 1000)
            upload_speed = random.randint(10, 100)
            ping = random.randint(10, 50)
            packets_sent = random.randint(1000, 5000)
            packets_received = random.randint(1000, 5000)
            
            print(f"[{timestamp}] Network Monitor Cycle #{counter}")
            print(f"Download: {download_speed} KB/s")
            print(f"Upload: {upload_speed} KB/s") 
            print(f"Ping: {ping} ms")
            print(f"Packets Sent: {packets_sent}")
            print(f"Packets Received: {packets_received}")
            print("-" * 40)
            
            time.sleep(3)
            
    except KeyboardInterrupt:
        print("\nNetwork Monitor stopped by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()