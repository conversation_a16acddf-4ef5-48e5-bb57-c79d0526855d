
import time
import random
import os

def main():
    os.system("title Network Monitor - Program 3")
    print("=== 网络监控程序启动 ===")
    print("监控网络连接和流量...")
    print()
    
    for i in range(100):
        timestamp = time.strftime("%H:%M:%S")
        connections = random.randint(50, 200)
        bandwidth = random.uniform(10.5, 99.9)
        packets = random.randint(1000, 5000)
        
        print(f"[{timestamp}] 活动连接: {connections}")
        print(f"[{timestamp}] 带宽使用: {bandwidth:.1f} Mbps")
        print(f"[{timestamp}] 数据包/秒: {packets}")
        print("-" * 40)
        time.sleep(3)

if __name__ == "__main__":
    main()
