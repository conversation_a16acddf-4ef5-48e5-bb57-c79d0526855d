#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序4 - 文件监控模拟
独立运行的示例程序，可在新窗口中显示
"""

import time
import random
import datetime
import sys
import os

def main():
    print("=== File Monitor (Program 4) ===")
    print("Running in separate window")
    print("Press Ctrl+C to exit")
    print()

    try:
        counter = 0
        file_types = ['.txt', '.log', '.py', '.json', '.xml']
        actions = ['Created', 'Modified', 'Deleted', 'Accessed']
        
        while True:
            counter += 1
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # 模拟文件操作
            filename = f"file_{random.randint(1, 100)}{random.choice(file_types)}"
            action = random.choice(actions)
            size = random.randint(1, 1000)
            
            print(f"[{timestamp}] File Monitor Event #{counter}")
            print(f"File: {filename}")
            print(f"Action: {action}")
            print(f"Size: {size} KB")
            print(f"Path: C:\\temp\\{filename}")
            print("-" * 40)
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\nFile Monitor stopped by user")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()