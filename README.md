# BIOS风格终端程序

这是一个使用Python开发的BIOS风格终端程序，直接启动到经典BIOS界面：

✅ **左侧控制菜单** - 完整的程序控制界面
✅ **右侧三个输出窗口** - OVERALL信息 + 两个程序输出窗口
✅ **经典BIOS界面设计** - 蓝色主题，复古风格
✅ **多程序管理** - 同时运行和监控多个子程序

## 📁 项目文件

### 🌟 核心文件
- **`stable_bios_terminal.py`** - 🏆 **主程序** - BIOS风格终端程序
- **`start.bat`** - 🚀 **Windows启动脚本**
- **`start.sh`** - 🚀 **Linux/macOS启动脚本**
- **`demo_program1.py`** - 示例程序1（系统监控）
- **`demo_program2.py`** - 示例程序2（日志生成器）
- **`requirements.txt`** - Python依赖包列表
- **`README.md`** - 使用说明（本文件）

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install windows-curses psutil
```

### 2. 运行程序

#### 🏆 推荐方法：使用启动脚本
```bash
# Windows
start.bat

# Linux/macOS
./start.sh
```

#### 🌟 直接运行
```bash
python stable_bios_terminal.py
```

程序将直接启动到BIOS界面，无任何警告信息。

### 3. 操作说明
- **↑/↓ 方向键**: 在菜单中上下移动选择
- **回车键**: 执行选中的菜单项
- **ESC键 或 Q键**: 退出程序

## 📋 功能菜单

### 主界面程序（在原窗口显示）
1. **Start Program 1** - 启动程序1（计数器演示）
2. **Start Program 2** - 启动程序2（时间显示演示）
3. **Stop Program 1** - 停止程序1
4. **Stop Program 2** - 停止程序2
5. **Clear Output 1** - 清空程序1的输出窗口
6. **Clear Output 2** - 清空程序2的输出窗口

### 🆕 独立窗口程序（新窗口运行）
7. **Start Program 3 (New Window)** - 启动网络监控程序（独立窗口）
8. **Start Program 4 (New Window)** - 启动文件监控程序（独立窗口）
9. **Stop Program 3** - 停止程序3
10. **Stop Program 4** - 停止程序4

### 系统功能
11. **System Info** - 显示系统信息
12. **Exit** - 退出程序

## 🎮 界面布局

程序直接启动到经典的BIOS风格界面：

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          BIOS TERMINAL - PYTHON VERSION                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─ CONTROL MENU ─────────┐ ┌─ OVERALL INFO ─────────────────────────────┐ │
│ │                        │ │                                            │ │
│ │ > Start Program 1      │ │ [12:34:56] BIOS Terminal System Ready      │ │
│ │   Start Program 2      │ │ [12:34:56] OVERALL Info Window Active      │ │
│ │   Stop Program 1       │ │                                            │ │
│ │   Stop Program 2       │ └────────────────────────────────────────────┘ │
│ │   Clear Output 1       │ ┌─ PROGRAM 1 OUTPUT ─────────────────────────┐ │
│ │   Clear Output 2       │ │                                            │ │
│ │   Start Program 3      │ │ [12:34:56] Program 1 Window Ready          │ │
│ │   Start Program 4      │ │                                            │ │
│ │   Stop Program 3       │ │                                            │ │
│ │   Stop Program 4       │ └────────────────────────────────────────────┘ │
│ │   System Info          │ ┌─ PROGRAM 2 OUTPUT ─────────────────────────┐ │
│ │   Exit                 │ │                                            │ │
│ │                        │ │ [12:34:56] Program 2 Window Ready          │ │
│ │                        │ │                                            │ │
│ └────────────────────────┘ └────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ BIOS Terminal Ready                                                         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 界面说明
- **左侧**: 控制菜单 - 使用方向键导航，回车执行
- **右上**: OVERALL信息窗口 - 显示系统状态和操作日志
- **右中**: PROGRAM 1输出窗口 - 显示程序1的实时输出
- **右下**: PROGRAM 2输出窗口 - 显示程序2的实时输出
- **底部**: 状态栏 - 显示当前系统状态

## 🎯 功能特性

- ✅ **经典BIOS界面** - 蓝色主题，复古风格
- ✅ **多程序管理** - 同时运行和监控多个子程序
- ✅ **实时输出显示** - 异步显示程序输出，界面流畅
- ✅ **键盘导航** - 方向键选择，回车执行
- ✅ **独立窗口支持** - 部分程序可在新窗口运行
- ✅ **系统信息显示** - 查看系统状态和版本信息

## 📦 系统要求

- Python 3.6+
- Windows: `pip install windows-curses psutil`
- Linux/macOS: `pip install psutil`

## 🎮 操作说明

- **↑/↓ 方向键**: 菜单导航
- **回车键**: 执行选中项
- **ESC/Q键**: 退出程序

## 🔧 故障排除

如果遇到问题：
1. 确保安装了依赖包：`pip install -r requirements.txt`
2. Windows用户需要：`pip install windows-curses`
3. 确保终端窗口足够大（建议80x25以上）

---

**享受经典BIOS风格的终端体验！** 🚀
