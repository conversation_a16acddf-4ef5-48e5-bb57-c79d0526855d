#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版终端界面框架 - Windows专用
通用终端UI框架，可为多个程序提供统一的界面
"""

import curses
import os
import queue
import subprocess
import sys
import threading
import time


class StableTerminal:
    def __init__(self):
        self.stdscr = None
        self.menu_win = None
        self.overall_win = None
        self.output1_win = None
        self.output2_win = None
        self.status_win = None
        
        # 菜单选项
        self.menu_items = [
            "Start Program 1",
            "Start Program 2", 
            "Stop Program 1",
            "Stop Program 2",
            "Clear Output 1",
            "Clear Output 2",
            "Start Program 3 (New Window)",
            "Start Program 4 (New Window)",
            "Stop Program 3",
            "Stop Program 4",
            "System Info",
            "Exit"
        ]
        self.current_menu = 0
        
        # 子程序管理
        self.processes = {
            'program1': None,
            'program2': None,
            'program3': None,
            'program4': None
        }

        # 外部程序状态跟踪
        self.external_programs = {
            'program3': False,
            'program4': False
        }

        # 输出缓冲区
        self.output_buffers = {
            'program0': [],
            'program1': [],
            'program2': []
        }

        # 输出队列
        self.output_queues = {
            'program0': queue.Queue(),
            'program1': queue.Queue(),
            'program2': queue.Queue()
        }
        
        self.running = True
        self.need_redraw = True
        self.last_terminal_size = (0, 0)

    def init_colors(self):
        """初始化颜色方案"""
        try:
            curses.start_color()
            curses.init_pair(1, curses.COLOR_WHITE, curses.COLOR_BLUE)
            curses.init_pair(2, curses.COLOR_BLACK, curses.COLOR_WHITE)
            curses.init_pair(3, curses.COLOR_WHITE, curses.COLOR_BLACK)
            curses.init_pair(4, curses.COLOR_GREEN, curses.COLOR_BLACK)
            curses.init_pair(5, curses.COLOR_RED, curses.COLOR_BLACK)
            curses.init_pair(6, curses.COLOR_CYAN, curses.COLOR_BLACK)
        except:
            pass
        
    def setup_windows(self):
        """设置窗口布局"""
        height, width = self.stdscr.getmaxyx()

        if height < 18 or width < 80:
            raise Exception(f"Terminal too small. Need at least 80x18, got {width}x{height}")

        # 删除旧窗口
        for win_name in ['menu_win', 'overall_win', 'output1_win', 'output2_win', 'status_win']:
            if hasattr(self, win_name) and getattr(self, win_name):
                delattr(self, win_name)

        # 计算布局 - 增加空白行
        menu_width = min(40, width // 2.5)
        right_width = width - menu_width - 1
        available_height = height - 4  # 标题栏1行 + 空白行1行 + 状态栏1行

        # OVERALL信息窗口
        overall_height = max(4, available_height // 4)
        self.overall_win = curses.newwin(overall_height, right_width, 2, menu_width + 1)

        # 计算剩余空间
        remaining_height = available_height - overall_height
        output_height = remaining_height // 2
        output1_y = 2 + overall_height
        output2_height = remaining_height - output_height
        output2_y = output1_y + output_height

        # 左侧菜单窗口
        self.menu_win = curses.newwin(available_height, menu_width, 2, 0)

        # 右侧输出窗口
        self.output1_win = curses.newwin(output_height, right_width, output1_y, menu_width + 1)
        
        if output2_height > 0:
            self.output2_win = curses.newwin(output2_height, right_width, output2_y, menu_width + 1)
        else:
            self.output2_win = curses.newwin(1, right_width, height - 2, menu_width + 1)

        # 状态栏
        self.status_win = curses.newwin(1, width, height - 1, 0)
        self.last_terminal_size = (height, width)

    def safe_addstr(self, win, y, x, text, attr=None):
        """安全地添加字符串"""
        try:
            max_y, max_x = win.getmaxyx()
            if y >= max_y or x >= max_x:
                return
            
            available_width = max_x - x - 1
            if available_width > 0:
                text = text[:available_width]
                if attr:
                    win.attron(attr)
                win.addstr(y, x, text)
                if attr:
                    win.attroff(attr)
        except curses.error:
            pass
        
    def draw_title(self):
        """绘制标题栏"""
        height, width = self.stdscr.getmaxyx()
        title = " TERMINAL INTERFACE - PYTHON VERSION "
        try:
            self.stdscr.attron(curses.color_pair(1))
            self.stdscr.addstr(0, 0, " " * width)
            title_x = max(0, (width - len(title)) // 2)
            self.stdscr.addstr(0, title_x, title[:width-1])
            self.stdscr.attroff(curses.color_pair(1))
        except curses.error:
            pass
        
    def draw_menu(self):
        """绘制左侧菜单"""
        try:
            self.menu_win.clear()
            self.menu_win.box()
            
            self.safe_addstr(self.menu_win, 1, 2, "CONTROL MENU", curses.color_pair(1))
            
            for i, item in enumerate(self.menu_items):
                y = i + 3
                max_y, max_x = self.menu_win.getmaxyx()
                if y >= max_y - 1:
                    break
                    
                if i == self.current_menu:
                    self.safe_addstr(self.menu_win, y, 1, f" {item} ", curses.color_pair(2))
                else:
                    self.safe_addstr(self.menu_win, y, 2, f" {item}", curses.color_pair(3))
            
            self.menu_win.refresh()
        except Exception:
            pass
        
    def draw_output_window(self, win, title, buffer):
        """绘制输出窗口"""
        try:
            win.clear()
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
            
            max_y, max_x = win.getmaxyx()
            max_lines = max(0, max_y - 3)
            start_line = max(0, len(buffer) - max_lines)
            
            for i, line in enumerate(buffer[start_line:]):
                if i < max_lines:
                    self.safe_addstr(win, i + 2, 2, line, curses.color_pair(4))
            
            win.refresh()
        except Exception:
            pass
        
    def draw_status(self):
        """绘制状态栏"""
        try:
            self.status_win.clear()

            prog1_status = "RUN" if self.processes['program1'] and self.processes['program1'].poll() is None else "STOP"
            prog2_status = "RUN" if self.processes['program2'] and self.processes['program2'].poll() is None else "STOP"

            latest_overall_msg = ""
            if self.output_buffers['program0']:
                last_msg = self.output_buffers['program0'][-1]
                if "] " in last_msg:
                    latest_overall_msg = last_msg.split("] ", 1)[1]
                else:
                    latest_overall_msg = last_msg

            controls = "UP/DOWN:Select ENTER:Execute R:Refresh ESC:Exit"
            programs = f"P1:{prog1_status} P2:{prog2_status}"

            max_y, max_x = self.status_win.getmaxyx()

            if latest_overall_msg:
                status_text = f" {controls} | {programs} | {latest_overall_msg} "
            else:
                status_text = f" {controls} | {programs} "

            if len(status_text) > max_x:
                status_text = status_text[:max_x - 3] + "..."

            try:
                self.status_win.attron(curses.color_pair(6))
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text, curses.color_pair(6))
                self.status_win.attroff(curses.color_pair(6))
            except:
                self.status_win.addstr(0, 0, " " * max_x)
                self.safe_addstr(self.status_win, 0, 0, status_text)

            self.status_win.refresh()
        except Exception:
            try:
                self.status_win.clear()
                self.status_win.addstr(0, 0, " Terminal Ready ")
                self.status_win.refresh()
            except:
                pass
        
    def update_outputs(self):
        """更新输出显示"""
        updated = False

        for program_name in ['program0', 'program1', 'program2']:
            try:
                while True:
                    line = self.output_queues[program_name].get_nowait()
                    timestamp = time.strftime("%H:%M:%S")
                    formatted_line = f"[{timestamp}] {line}"
                    self.output_buffers[program_name].append(formatted_line)

                    if len(self.output_buffers[program_name]) > 100:
                        self.output_buffers[program_name] = self.output_buffers[program_name][-50:]

                    updated = True
            except queue.Empty:
                pass

        if updated:
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])
            self.draw_status()

    def start_program(self, program_name: str):
        """启动程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            self.output_queues['program0'].put(f"{program_name} already running")
            return
            
        try:
            if program_name == 'program1':
                cmd = [sys.executable, '-u', 'demo_program1.py']
            else:
                cmd = [sys.executable, '-u', 'demo_program2.py']

            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,
                env=env
            )
            self.processes[program_name] = process
            
            thread = threading.Thread(
                target=self.read_process_output,
                args=(program_name, process),
                daemon=True
            )
            thread.start()
            
            self.output_queues['program0'].put(f"{program_name} started successfully")

        except Exception as e:
            self.output_queues['program0'].put(f"{program_name} start failed: {str(e)}")

    def read_process_output(self, program_name: str, process):
        """读取子程序输出"""
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                self.output_queues[program_name].put(line.strip())
        except Exception as e:
            self.output_queues[program_name].put(f"Read error: {str(e)}")
        finally:
            if process.stdout:
                process.stdout.close()
            
    def stop_program(self, program_name: str):
        """停止程序"""
        if self.processes[program_name] and self.processes[program_name].poll() is None:
            try:
                self.processes[program_name].terminate()
                self.processes[program_name].wait(timeout=3)
                self.output_queues['program0'].put(f"{program_name} stopped")
            except subprocess.TimeoutExpired:
                self.processes[program_name].kill()
                self.output_queues['program0'].put(f"{program_name} force killed")
            except Exception as e:
                self.output_queues['program0'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['program0'].put(f"{program_name} not running")
            
    def clear_output(self, program_name: str):
        """清空输出缓冲区"""
        self.output_buffers[program_name].clear()
        self.output_queues['program0'].put(f"{program_name} output cleared")

        if program_name == 'program1':
            self.force_clear_window(self.output1_win, "PROGRAM 1 OUTPUT")
        elif program_name == 'program2':
            self.force_clear_window(self.output2_win, "PROGRAM 2 OUTPUT")
        elif program_name == 'program0':
            self.force_clear_window(self.overall_win, "OVERALL INFO")

    def force_clear_window(self, win, title):
        """强制清空窗口"""
        try:
            max_y, max_x = win.getmaxyx()
            win.clear()
            win.erase()

            for y in range(max_y):
                for x in range(max_x):
                    try:
                        win.addch(y, x, ' ')
                    except:
                        pass

            win.clear()
            win.erase()
            win.box()
            self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
            win.noutrefresh()
            curses.doupdate()
            win.refresh()

        except Exception:
            try:
                win.clear()
                win.box()
                self.safe_addstr(win, 0, 2, f" {title} ", curses.color_pair(1))
                win.refresh()
            except:
                pass

    def show_system_info(self):
        """显示系统信息"""
        try:
            import platform
            import psutil
            
            info = [
                f"System: {platform.system()} {platform.release()}",
                f"Python: {platform.python_version()}",
                f"CPU: {psutil.cpu_count()} cores",
                f"Memory: {psutil.virtual_memory().total // (1024**3)} GB"
            ]
            
            for line in info:
                self.output_queues['program0'].put(line)
                
        except ImportError:
            self.output_queues['program0'].put("System info requires psutil package")
        except Exception as e:
            self.output_queues['program0'].put(f"System info error: {str(e)}")

    def handle_menu_action(self):
        """处理菜单操作"""
        try:
            action = self.menu_items[self.current_menu]
            
            if action == "Start Program 1":
                self.start_program('program1')
            elif action == "Start Program 2":
                self.start_program('program2')
            elif action == "Stop Program 1":
                self.stop_program('program1')
            elif action == "Stop Program 2":
                self.stop_program('program2')
            elif action == "Clear Output 1":
                self.clear_output('program1')
            elif action == "Clear Output 2":
                self.clear_output('program2')
            elif action == "Start Program 3 (New Window)":
                self.start_program_new_window('program3')
            elif action == "Start Program 4 (New Window)":
                self.start_program_new_window('program4')
            elif action == "Stop Program 3":
                self.stop_program_external('program3')
            elif action == "Stop Program 4":
                self.stop_program_external('program4')
            elif action == "System Info":
                self.show_system_info()
            elif action == "Exit":
                self.running = False

            self.draw_status()

        except Exception as e:
            self.output_queues['program0'].put(f"Menu error: {e}")

    def start_program_new_window(self, program_name: str):
        """在新窗口启动程序"""
        if self.external_programs[program_name]:
            self.output_queues['program0'].put(f"{program_name} already running in separate window")
            return

        try:
            if program_name == 'program3':
                # 创建网络监控程序
                program_content = '''
import time
import random
import os

def main():
    os.system("title Network_Monitor_Program_3")
    print("=== 网络监控程序启动 ===")
    print("监控网络连接和流量...")
    print()

    for i in range(100):
        timestamp = time.strftime("%H:%M:%S")
        connections = random.randint(50, 200)
        bandwidth = random.uniform(10.5, 99.9)
        packets = random.randint(1000, 5000)

        print(f"[{timestamp}] 活动连接: {connections}")
        print(f"[{timestamp}] 带宽使用: {bandwidth:.1f} Mbps")
        print(f"[{timestamp}] 数据包/秒: {packets}")
        print("-" * 40)
        time.sleep(3)

if __name__ == "__main__":
    main()
'''
                title = "Network_Monitor_Program_3"
            else:  # program4
                # 创建文件监控程序
                program_content = '''
import time
import random
import os

def main():
    os.system("title File_Monitor_Program_4")
    print("=== 文件系统监控程序启动 ===")
    print("监控文件系统变化...")
    print()

    actions = ["创建", "修改", "删除", "移动"]
    file_types = [".txt", ".log", ".dat", ".tmp", ".cfg"]

    for i in range(100):
        timestamp = time.strftime("%H:%M:%S")
        action = random.choice(actions)
        file_type = random.choice(file_types)
        file_num = random.randint(1, 999)
        size = random.randint(1, 1024)

        print(f"[{timestamp}] 文件{action}: file_{file_num:03d}{file_type}")
        print(f"[{timestamp}] 文件大小: {size} KB")
        print(f"[{timestamp}] 操作状态: 成功")
        print("-" * 40)
        time.sleep(2)

if __name__ == "__main__":
    main()
'''
                title = "File_Monitor_Program_4"

            # 创建临时Python文件
            temp_file = f"temp_{program_name}.py"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(program_content)

            # 使用更简单的方式在新窗口启动程序
            cmd = f'start "Program_{program_name[-1]}" cmd /k "python {temp_file}"'

            process = subprocess.Popen(
                cmd,
                shell=True
            )

            # 标记外部程序为运行状态
            self.external_programs[program_name] = True
            self.output_queues['program0'].put(f"{program_name} started in new window")

        except Exception as e:
            self.output_queues['program0'].put(f"{program_name} start failed: {str(e)}")

    def stop_program_external(self, program_name: str):
        """停止外部窗口程序"""
        if self.external_programs[program_name]:
            try:
                # 使用taskkill命令终止Python进程
                # 这会关闭运行我们程序的Python进程
                import os
                temp_file = f"temp_{program_name}.py"

                # 尝试通过窗口标题杀死进程
                title = f"Program_{program_name[-1]}"
                cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'

                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                # 标记程序为停止状态
                self.external_programs[program_name] = False

                if result.returncode == 0:
                    self.output_queues['program0'].put(f"{program_name} window terminated successfully")
                else:
                    self.output_queues['program0'].put(f"{program_name} marked as stopped (window may need manual close)")

                # 清理临时文件
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except:
                    pass

            except Exception as e:
                self.external_programs[program_name] = False
                self.output_queues['program0'].put(f"Stop {program_name} error: {str(e)}")
        else:
            self.output_queues['program0'].put(f"{program_name} not running")

    def run(self):
        """主运行循环"""
        try:
            self.stdscr = curses.initscr()
            curses.noecho()
            curses.cbreak()
            self.stdscr.keypad(True)
            curses.curs_set(0)
            self.stdscr.timeout(200)

            self.init_colors()
            self.setup_windows()

            self.draw_title()
            self.draw_menu()
            self.draw_output_window(self.overall_win, "OVERALL INFO", self.output_buffers['program0'])
            self.draw_output_window(self.output1_win, "PROGRAM 1 OUTPUT", self.output_buffers['program1'])
            self.draw_output_window(self.output2_win, "PROGRAM 2 OUTPUT", self.output_buffers['program2'])
            self.draw_status()
            self.stdscr.refresh()

            self.output_queues['program0'].put("Terminal System Ready")
            self.output_queues['program1'].put("Program 1 Window Ready")
            self.output_queues['program2'].put("Program 2 Window Ready")

            last_menu = -1
            refresh_counter = 0

            while self.running:
                try:
                    # 检查窗口大小变化
                    new_height, new_width = self.stdscr.getmaxyx()
                    if new_height != self.height or new_width != self.width:
                        self.height, self.width = new_height, new_width
                        self.stdscr.clear()
                        self.draw_layout()
                        refresh_counter = 0  # 强制重绘

                    self.update_outputs()
                    refresh_counter += 1

                    if last_menu != self.current_menu or refresh_counter % 10 == 0:
                        self.draw_menu()
                        last_menu = self.current_menu

                    if refresh_counter % 5 == 0:
                        self.draw_status()

                    key = self.stdscr.getch()

                    if key == curses.KEY_UP:
                        self.current_menu = (self.current_menu - 1) % len(self.menu_items)
                    elif key == curses.KEY_DOWN:
                        self.current_menu = (self.current_menu + 1) % len(self.menu_items)
                    elif key == ord('\n') or key == ord('\r'):
                        self.handle_menu_action()
                    elif key == 27 or key == ord('q') or key == ord('Q'):
                        self.running = False

                except KeyboardInterrupt:
                    self.running = False
                except Exception as e:
                    try:
                        self.output_queues['program0'].put(f"Error: {e}")
                    except:
                        pass

        except Exception as e:
            print(f"Fatal error: {e}")
        finally:
            try:
                # 停止内部程序
                for process in [self.processes['program1'], self.processes['program2']]:
                    if process and process.poll() is None:
                        process.terminate()

                # 停止外部程序
                for program_name in ['program3', 'program4']:
                    if self.external_programs[program_name]:
                        try:
                            title = f"Program_{program_name[-1]}"
                            cmd = f'taskkill /FI "WINDOWTITLE:*{title}*" /F /T'
                            subprocess.run(cmd, shell=True, capture_output=True)
                        except:
                            pass

                # 清理临时文件
                for temp_file in ['temp_program3.py', 'temp_program4.py']:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except:
                        pass
            except:
                pass
            try:
                curses.endwin()
            except:
                pass


def main():
    """主函数"""
    try:
        os.system('chcp 65001 >nul')
        print("Starting Terminal Interface...")
        print("Make sure your terminal is at least 80x18 characters")
        time.sleep(1)

        terminal = StableTerminal()
        terminal.run()

        print("Terminal Interface exited.")

    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have installed: pip install windows-curses psutil")


if __name__ == "__main__":
    main()